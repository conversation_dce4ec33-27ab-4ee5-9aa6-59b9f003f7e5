"""
AI Gen Hub 调试页面API路由

提供开发和调试相关的API端点，包括：
- 系统状态监控
- API接口测试
- 日志查看器
- 配置信息展示
- 性能指标
- 开发工具

注意：此模块仅在开发和测试环境中可用，生产环境将被禁用
"""

import asyncio
import json
import os
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional

from fastapi import APIRouter, Request, HTTPException, Depends, Query
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.templating import Jinja2Templates
from pydantic import BaseModel

from ai_gen_hub.config import get_settings
from ai_gen_hub.core.logging import get_logger
from ai_gen_hub.monitoring import HealthManager

# JWT相关导入
try:
    import jwt
    JWT_AVAILABLE = True
except ImportError:
    JWT_AVAILABLE = False

# 尝试导入psutil，如果失败则提供备用实现
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False
    psutil = None

# 创建路由器
router = APIRouter()

# 设置模板
templates = Jinja2Templates(directory="src/ai_gen_hub/templates")

# 日志记录器
logger = get_logger(__name__)


class SystemInfo(BaseModel):
    """系统信息模型"""
    cpu_percent: float
    memory_percent: float
    memory_total: int
    memory_available: int
    disk_percent: float
    disk_total: int
    disk_free: int
    uptime: float
    process_count: int
    timestamp: float


class APIEndpoint(BaseModel):
    """API端点信息模型"""
    path: str
    method: str
    name: str
    description: str
    tags: List[str]


class TokenGenerateRequest(BaseModel):
    """生成Token请求模型"""
    user_id: str = "api_user"
    expire_minutes: int = 1440
    scope: str = "api_access"


class TokenVerifyRequest(BaseModel):
    """验证Token请求模型"""
    token: str
    parameters: Dict[str, Any]


class LogEntry(BaseModel):
    """日志条目模型"""
    timestamp: str
    level: str
    logger: str
    message: str
    extra: Dict[str, Any]


def check_debug_access(request: Request) -> bool:
    """检查调试页面访问权限
    
    Args:
        request: FastAPI请求对象
        
    Returns:
        是否允许访问
        
    Raises:
        HTTPException: 当访问被拒绝时
    """
    settings = getattr(request.app.state, "settings", None)
    if not settings:
        raise HTTPException(status_code=500, detail="配置未初始化")
    
    # 检查环境
    if settings.environment.lower() == "production":
        raise HTTPException(
            status_code=403, 
            detail="调试页面在生产环境中不可用"
        )
    
    # 检查调试模式
    if not settings.debug:
        raise HTTPException(
            status_code=403, 
            detail="调试页面需要启用调试模式"
        )
    
    return True


def get_system_info() -> SystemInfo:
    """获取系统信息

    Returns:
        系统信息对象
    """
    try:
        if not PSUTIL_AVAILABLE:
            # 如果psutil不可用，返回模拟数据
            return SystemInfo(
                cpu_percent=0.0,
                memory_percent=0.0,
                memory_total=0,
                memory_available=0,
                disk_percent=0.0,
                disk_total=0,
                disk_free=0,
                uptime=0.0,
                process_count=0,
                timestamp=time.time()
            )

        # CPU信息
        cpu_percent = psutil.cpu_percent(interval=1)

        # 内存信息
        memory = psutil.virtual_memory()

        # 磁盘信息
        disk = psutil.disk_usage('/')

        # 系统运行时间
        boot_time = psutil.boot_time()
        uptime = time.time() - boot_time

        # 进程数量
        process_count = len(psutil.pids())

        return SystemInfo(
            cpu_percent=cpu_percent,
            memory_percent=memory.percent,
            memory_total=memory.total,
            memory_available=memory.available,
            disk_percent=disk.percent,
            disk_total=disk.total,
            disk_free=disk.free,
            uptime=uptime,
            process_count=process_count,
            timestamp=time.time()
        )
    except Exception as e:
        logger.error("获取系统信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"获取系统信息失败: {str(e)}")


def get_api_endpoints(app) -> List[APIEndpoint]:
    """获取所有API端点信息
    
    Args:
        app: FastAPI应用实例
        
    Returns:
        API端点列表
    """
    endpoints = []
    
    for route in app.routes:
        if hasattr(route, 'methods') and hasattr(route, 'path'):
            for method in route.methods:
                if method != 'HEAD':  # 排除HEAD方法
                    endpoint = APIEndpoint(
                        path=route.path,
                        method=method,
                        name=getattr(route, 'name', ''),
                        description=getattr(route, 'description', ''),
                        tags=getattr(route, 'tags', []),
                        parameters={}  # 可以进一步解析参数信息
                    )
                    endpoints.append(endpoint)
    
    return sorted(endpoints, key=lambda x: (x.path, x.method))


@router.get("/", response_class=HTMLResponse)
async def debug_dashboard(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """调试仪表板主页
    
    显示调试页面的主界面，包含所有功能模块的导航
    """
    settings = getattr(request.app.state, "settings", None)
    
    context = {
        "request": request,
        "title": "AI Gen Hub 调试仪表板",
        "app_name": settings.app_name if settings else "AI Gen Hub",
        "app_version": settings.app_version if settings else "Unknown",
        "environment": settings.environment if settings else "Unknown",
        "timestamp": datetime.now().isoformat()
    }
    
    return templates.TemplateResponse("debug/dashboard.html", context)


@router.get("/api/system/info")
async def get_system_status(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取系统状态信息
    
    Returns:
        系统状态详细信息
    """
    try:
        # 基础系统信息
        system_info = get_system_info()
        
        # 应用状态信息
        settings = getattr(request.app.state, "settings", None)
        health_manager = getattr(request.app.state, "health_manager", None)
        
        app_info = {
            "name": settings.app_name if settings else "Unknown",
            "version": settings.app_version if settings else "Unknown",
            "environment": settings.environment if settings else "Unknown",
            "debug_mode": settings.debug if settings else False,
            "start_time": getattr(request.app.state, "start_time", time.time()),
        }
        
        # 健康检查状态
        health_status = None
        if health_manager:
            try:
                health_report = await health_manager.check_health()
                health_status = {
                    "overall_status": health_report.overall_status,
                    "checks": [
                        {
                            "name": check.name,
                            "status": check.status,
                            "message": check.message,
                            "duration": check.duration
                        }
                        for check in health_report.checks
                    ]
                }
            except Exception as e:
                health_status = {"error": str(e)}
        
        return {
            "system": system_info.dict(),
            "application": app_info,
            "health": health_status,
            "timestamp": time.time()
        }
        
    except Exception as e:
        logger.error("获取系统状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/endpoints")
async def get_endpoints(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取所有API端点信息
    
    Returns:
        API端点列表
    """
    try:
        endpoints = get_api_endpoints(request.app)
        return {
            "endpoints": [endpoint.dict() for endpoint in endpoints],
            "total": len(endpoints),
            "timestamp": time.time()
        }
    except Exception as e:
        logger.error("获取API端点信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/system")
async def system_monitor_page(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """系统监控页面"""
    context = {
        "request": request,
        "title": "系统状态监控",
        "page": "system"
    }
    return templates.TemplateResponse("debug/system.html", context)


@router.get("/api-test")
async def api_test_page(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """API测试页面"""
    context = {
        "request": request,
        "title": "API接口测试",
        "page": "api-test"
    }
    return templates.TemplateResponse("debug/api_test.html", context)


@router.get("/logs")
async def logs_page(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """日志查看页面"""
    context = {
        "request": request,
        "title": "日志查看器",
        "page": "logs"
    }
    return templates.TemplateResponse("debug/logs.html", context)


@router.get("/config")
async def config_page(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """配置信息页面"""
    context = {
        "request": request,
        "title": "配置信息",
        "page": "config"
    }
    return templates.TemplateResponse("debug/config.html", context)


@router.get("/metrics")
async def metrics_page(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """性能指标页面"""
    context = {
        "request": request,
        "title": "性能指标",
        "page": "metrics"
    }
    return templates.TemplateResponse("debug/metrics.html", context)


@router.get("/tools")
async def tools_page(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """开发工具页面"""
    context = {
        "request": request,
        "title": "开发工具",
        "page": "tools"
    }
    return templates.TemplateResponse("debug/tools.html", context)


@router.get("/tokens")
async def token_manager_page(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """Token管理页面"""
    context = {
        "request": request,
        "title": "Token管理",
        "page": "tokens"
    }
    return templates.TemplateResponse("debug/token_manager.html", context)


@router.get("/api/system/detailed")
async def get_detailed_system_info(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取详细的系统信息

    Returns:
        详细的系统信息，包括进程列表、网络连接等
    """
    try:
        if not PSUTIL_AVAILABLE:
            return {"error": "psutil库不可用，无法获取详细系统信息"}

        # 基础系统信息
        system_info = get_system_info()

        # CPU详细信息
        cpu_info = {
            "count": psutil.cpu_count(),
            "count_logical": psutil.cpu_count(logical=True),
            "freq": psutil.cpu_freq()._asdict() if psutil.cpu_freq() else None,
            "percent_per_cpu": psutil.cpu_percent(percpu=True),
            "times": psutil.cpu_times()._asdict(),
            "stats": psutil.cpu_stats()._asdict()
        }

        # 内存详细信息
        memory_info = {
            "virtual": psutil.virtual_memory()._asdict(),
            "swap": psutil.swap_memory()._asdict()
        }

        # 磁盘详细信息
        disk_info = {
            "usage": psutil.disk_usage('/')._asdict(),
            "io_counters": psutil.disk_io_counters()._asdict() if psutil.disk_io_counters() else None,
            "partitions": [p._asdict() for p in psutil.disk_partitions()]
        }

        # 网络信息
        network_info = {
            "io_counters": psutil.net_io_counters()._asdict() if psutil.net_io_counters() else None,
            "connections": len(psutil.net_connections()),
            "interfaces": {name: addr._asdict() for name, addrs in psutil.net_if_addrs().items() for addr in addrs}
        }

        # 进程信息（前10个占用CPU最多的进程）
        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent', 'status']):
            try:
                processes.append(proc.info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        # 按CPU使用率排序，取前10个
        processes = sorted(processes, key=lambda x: x.get('cpu_percent', 0), reverse=True)[:10]

        return {
            "basic": system_info.dict(),
            "cpu": cpu_info,
            "memory": memory_info,
            "disk": disk_info,
            "network": network_info,
            "top_processes": processes,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取详细系统信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/system/processes")
async def get_system_processes(
    request: Request,
    limit: int = Query(50, description="返回进程数量限制"),
    sort_by: str = Query("cpu_percent", description="排序字段"),
    _: bool = Depends(check_debug_access)
):
    """获取系统进程列表

    Args:
        limit: 返回的进程数量限制
        sort_by: 排序字段 (cpu_percent, memory_percent, pid, name)

    Returns:
        进程列表
    """
    try:
        if not PSUTIL_AVAILABLE:
            return {"error": "psutil库不可用，无法获取进程信息"}

        processes = []
        for proc in psutil.process_iter(['pid', 'name', 'cpu_percent', 'memory_percent',
                                       'status', 'create_time', 'cmdline']):
            try:
                proc_info = proc.info
                proc_info['create_time'] = datetime.fromtimestamp(proc_info['create_time']).isoformat()
                proc_info['cmdline'] = ' '.join(proc_info['cmdline'][:3]) if proc_info['cmdline'] else ''
                processes.append(proc_info)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                pass

        # 排序
        reverse = sort_by in ['cpu_percent', 'memory_percent']
        processes = sorted(processes, key=lambda x: x.get(sort_by, 0), reverse=reverse)

        return {
            "processes": processes[:limit],
            "total": len(processes),
            "sort_by": sort_by,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取进程列表失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/database/status")
async def get_database_status(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取数据库连接状态

    Returns:
        数据库连接状态信息
    """
    try:
        settings = getattr(request.app.state, "settings", None)
        if not settings or not settings.database:
            return {
                "status": "not_configured",
                "message": "数据库未配置",
                "timestamp": time.time()
            }

        # 这里可以添加实际的数据库连接测试
        # 目前返回配置信息（脱敏）
        db_config = {
            "host": settings.database.host,
            "port": settings.database.port,
            "database": settings.database.database,
            "user": settings.database.user,
            "password": "***" if settings.database.password else None,
            "pool_size": settings.database.pool_size,
            "max_overflow": settings.database.max_overflow,
        }

        return {
            "status": "configured",
            "config": db_config,
            "message": "数据库已配置",
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取数据库状态失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/cache/status")
async def get_cache_status(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取缓存状态

    Returns:
        缓存状态信息
    """
    try:
        cache = getattr(request.app.state, "cache", None)
        settings = getattr(request.app.state, "settings", None)

        if not cache:
            return {
                "status": "not_available",
                "message": "缓存未初始化",
                "timestamp": time.time()
            }

        # 获取缓存统计信息
        cache_stats = await cache.get_stats()

        # Redis配置信息（脱敏）
        redis_config = None
        if settings and settings.redis:
            redis_config = {
                "host": settings.redis.host,
                "port": settings.redis.port,
                "db": settings.redis.db,
                "password": "***" if settings.redis.password else None,
                "max_connections": settings.redis.max_connections,
            }

        return {
            "status": "available",
            "stats": {
                "hits": cache_stats.hits,
                "misses": cache_stats.misses,
                "hit_rate": cache_stats.hit_rate,
                "total_size": cache_stats.total_size
            },
            "redis_config": redis_config,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取缓存状态失败", error=str(e))
        return {
            "status": "error",
            "message": str(e),
            "timestamp": time.time()
        }


@router.post("/api/test-endpoint")
async def test_api_endpoint(
    request: Request,
    endpoint_data: dict,
    _: bool = Depends(check_debug_access)
):
    """测试API端点

    Args:
        endpoint_data: 包含测试信息的字典
            - url: 要测试的URL
            - method: HTTP方法
            - headers: 请求头
            - body: 请求体
            - params: 查询参数

    Returns:
        测试结果
    """
    import httpx

    try:
        url = endpoint_data.get('url', '')
        method = endpoint_data.get('method', 'GET').upper()
        headers = endpoint_data.get('headers', {})
        body = endpoint_data.get('body', '')
        params = endpoint_data.get('params', {})

        # 如果是相对URL，添加基础URL
        if url.startswith('/'):
            base_url = f"{request.url.scheme}://{request.url.netloc}"
            url = base_url + url

        # 准备请求数据
        request_data = {
            'method': method,
            'url': url,
            'headers': headers,
            'params': params,
            'timeout': 30.0
        }

        # 处理请求体
        if body and method in ['POST', 'PUT', 'PATCH']:
            try:
                # 尝试解析为JSON
                request_data['json'] = json.loads(body)
            except json.JSONDecodeError:
                # 如果不是JSON，作为文本发送
                request_data['content'] = body
                if 'content-type' not in [k.lower() for k in headers.keys()]:
                    headers['Content-Type'] = 'text/plain'

        start_time = time.time()

        # 发送请求
        async with httpx.AsyncClient() as client:
            response = await client.request(**request_data)

        end_time = time.time()
        response_time = (end_time - start_time) * 1000  # 转换为毫秒

        # 尝试解析响应体
        try:
            response_json = response.json()
        except:
            response_json = None

        return {
            "success": True,
            "request": {
                "method": method,
                "url": url,
                "headers": dict(headers),
                "params": params,
                "body": body
            },
            "response": {
                "status_code": response.status_code,
                "status_text": response.reason_phrase,
                "headers": dict(response.headers),
                "body": response.text,
                "json": response_json,
                "size": len(response.content)
            },
            "timing": {
                "response_time": response_time,
                "timestamp": time.time()
            }
        }

    except Exception as e:
        logger.error("API端点测试失败", error=str(e), endpoint=endpoint_data)
        return {
            "success": False,
            "error": str(e),
            "request": endpoint_data,
            "timestamp": time.time()
        }


@router.get("/api/endpoints/detailed")
async def get_detailed_endpoints(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取详细的API端点信息

    Returns:
        详细的API端点列表，包括参数信息
    """
    try:
        endpoints = []

        for route in request.app.routes:
            if hasattr(route, 'methods') and hasattr(route, 'path'):
                for method in route.methods:
                    if method != 'HEAD':  # 排除HEAD方法
                        # 获取路由函数
                        endpoint_func = getattr(route, 'endpoint', None)

                        # 获取参数信息
                        parameters = {}
                        if endpoint_func:
                            import inspect
                            sig = inspect.signature(endpoint_func)
                            for param_name, param in sig.parameters.items():
                                if param_name not in ['request', 'self']:
                                    param_info = {
                                        'name': param_name,
                                        'type': str(param.annotation) if param.annotation != inspect.Parameter.empty else 'Any',
                                        'default': str(param.default) if param.default != inspect.Parameter.empty else None,
                                        'required': param.default == inspect.Parameter.empty
                                    }
                                    parameters[param_name] = param_info

                        # 获取文档字符串
                        description = ""
                        if endpoint_func and endpoint_func.__doc__:
                            description = endpoint_func.__doc__.strip().split('\n')[0]

                        endpoint = {
                            "path": route.path,
                            "method": method,
                            "name": getattr(route, 'name', ''),
                            "description": description,
                            "tags": getattr(route, 'tags', []),
                            "parameters": parameters,
                            "path_params": [p for p in route.path.split('/') if p.startswith('{') and p.endswith('}')],
                            "requires_auth": any(dep.__name__ == 'get_user_id' for dep in getattr(route, 'dependencies', [])),
                        }
                        endpoints.append(endpoint)

        # 按路径和方法排序
        endpoints = sorted(endpoints, key=lambda x: (x['path'], x['method']))

        # 按标签分组
        grouped_endpoints = {}
        for endpoint in endpoints:
            tags = endpoint['tags'] or ['未分类']
            for tag in tags:
                if tag not in grouped_endpoints:
                    grouped_endpoints[tag] = []
                grouped_endpoints[tag].append(endpoint)

        return {
            "endpoints": endpoints,
            "grouped": grouped_endpoints,
            "total": len(endpoints),
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取详细端点信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/openapi-spec")
async def get_openapi_spec(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取OpenAPI规范

    Returns:
        OpenAPI规范JSON
    """
    try:
        # 获取FastAPI应用的OpenAPI规范
        openapi_schema = request.app.openapi()
        return openapi_schema

    except Exception as e:
        logger.error("获取OpenAPI规范失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/logs")
async def get_logs(
    request: Request,
    level: Optional[str] = Query(None, description="日志级别过滤"),
    search: Optional[str] = Query(None, description="搜索关键词"),
    limit: int = Query(100, description="返回日志条数"),
    offset: int = Query(0, description="偏移量"),
    _: bool = Depends(check_debug_access)
):
    """获取应用日志

    Args:
        level: 日志级别过滤 (DEBUG, INFO, WARNING, ERROR, CRITICAL)
        search: 搜索关键词
        limit: 返回的日志条数
        offset: 偏移量

    Returns:
        日志列表
    """
    try:
        # 这里实现日志读取逻辑
        # 由于我们使用的是structlog，可能需要从日志文件或内存中读取

        # 模拟日志数据（实际实现中应该从真实的日志源读取）
        import random
        from datetime import datetime, timedelta

        log_levels = ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']
        log_messages = [
            "应用启动成功",
            "处理用户请求",
            "数据库连接建立",
            "缓存命中",
            "API调用完成",
            "配置重新加载",
            "健康检查通过",
            "供应商响应超时",
            "认证失败",
            "系统资源不足"
        ]

        logs = []
        current_time = datetime.now()

        for i in range(limit):
            log_level = random.choice(log_levels)
            if level and log_level != level.upper():
                continue

            message = random.choice(log_messages)
            if search and search.lower() not in message.lower():
                continue

            log_entry = {
                "timestamp": (current_time - timedelta(minutes=i)).isoformat(),
                "level": log_level,
                "logger": f"ai_gen_hub.{random.choice(['api', 'services', 'providers', 'cache'])}",
                "message": message,
                "extra": {
                    "request_id": f"req_{random.randint(1000, 9999)}",
                    "user_id": f"user_{random.randint(1, 100)}" if random.random() > 0.5 else None
                }
            }
            logs.append(log_entry)

        return {
            "logs": logs,
            "total": len(logs),
            "level_filter": level,
            "search_filter": search,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取日志失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/logs/levels")
async def get_log_levels(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取可用的日志级别

    Returns:
        日志级别列表和统计
    """
    try:
        # 返回标准的日志级别
        levels = [
            {"name": "DEBUG", "description": "调试信息", "color": "#6b7280"},
            {"name": "INFO", "description": "一般信息", "color": "#3b82f6"},
            {"name": "WARNING", "description": "警告信息", "color": "#f59e0b"},
            {"name": "ERROR", "description": "错误信息", "color": "#ef4444"},
            {"name": "CRITICAL", "description": "严重错误", "color": "#dc2626"}
        ]

        # 这里可以添加实际的日志级别统计
        for level in levels:
            level["count"] = random.randint(0, 1000)  # 模拟数据

        return {
            "levels": levels,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取日志级别失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/logs/stats")
async def get_log_stats(
    request: Request,
    hours: int = Query(24, description="统计时间范围（小时）"),
    _: bool = Depends(check_debug_access)
):
    """获取日志统计信息

    Args:
        hours: 统计时间范围（小时）

    Returns:
        日志统计信息
    """
    try:
        # 模拟日志统计数据
        import random
        from datetime import datetime, timedelta

        current_time = datetime.now()
        stats = {
            "time_range": {
                "start": (current_time - timedelta(hours=hours)).isoformat(),
                "end": current_time.isoformat(),
                "hours": hours
            },
            "total_logs": random.randint(1000, 10000),
            "by_level": {
                "DEBUG": random.randint(100, 1000),
                "INFO": random.randint(500, 2000),
                "WARNING": random.randint(50, 500),
                "ERROR": random.randint(10, 100),
                "CRITICAL": random.randint(0, 10)
            },
            "by_hour": [],
            "top_loggers": [
                {"name": "ai_gen_hub.api", "count": random.randint(100, 500)},
                {"name": "ai_gen_hub.services", "count": random.randint(50, 300)},
                {"name": "ai_gen_hub.providers", "count": random.randint(30, 200)},
                {"name": "ai_gen_hub.cache", "count": random.randint(20, 150)}
            ]
        }

        # 生成按小时的统计
        for i in range(min(hours, 24)):
            hour_time = current_time - timedelta(hours=i)
            stats["by_hour"].append({
                "hour": hour_time.strftime("%H:00"),
                "timestamp": hour_time.isoformat(),
                "count": random.randint(10, 200)
            })

        stats["by_hour"].reverse()  # 按时间顺序排列

        return stats

    except Exception as e:
        logger.error("获取日志统计失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/config")
async def get_configuration(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取应用配置信息（脱敏处理）

    Returns:
        脱敏后的配置信息
    """
    try:
        settings = getattr(request.app.state, "settings", None)
        if not settings:
            return {"error": "配置未初始化"}

        def mask_sensitive_value(key: str, value: Any) -> Any:
            """脱敏处理敏感信息"""
            if value is None:
                return None

            sensitive_keys = [
                'password', 'secret', 'key', 'token', 'api_key',
                'private_key', 'auth', 'credential'
            ]

            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                if isinstance(value, str) and value:
                    return "***" + value[-4:] if len(value) > 4 else "***"
                else:
                    return "***"

            return value

        def process_config_section(obj: Any, section_name: str) -> Dict[str, Any]:
            """处理配置段落"""
            if obj is None:
                return {"status": "未配置"}

            result = {}

            if hasattr(obj, '__dict__'):
                for key, value in obj.__dict__.items():
                    if key.startswith('_'):
                        continue

                    if hasattr(value, '__dict__'):
                        # 嵌套对象
                        result[key] = process_config_section(value, key)
                    elif isinstance(value, (list, tuple)):
                        # 列表类型
                        result[key] = [mask_sensitive_value(key, item) for item in value]
                    else:
                        # 普通值
                        result[key] = mask_sensitive_value(key, value)
            else:
                result = mask_sensitive_value(section_name, obj)

            return result

        # 处理各个配置段落
        config_data = {
            "application": {
                "name": settings.app_name,
                "version": settings.app_version,
                "environment": settings.environment,
                "debug": settings.debug,
                "host": settings.api_host,
                "port": settings.api_port,
                "workers": settings.api_workers
            },
            "database": process_config_section(settings.database, "database"),
            "redis": process_config_section(settings.redis, "redis"),
            "cache": process_config_section(settings.cache, "cache"),
            "monitoring": process_config_section(settings.monitoring, "monitoring"),
            "security": process_config_section(settings.security, "security"),
            "storage": process_config_section(settings.storage, "storage"),
            "performance": process_config_section(settings.performance, "performance"),
            "features": process_config_section(settings.features, "features"),
            "providers": {
                "openai": process_config_section(settings.openai, "openai"),
                "google_ai": process_config_section(settings.google_ai, "google_ai"),
                "anthropic": process_config_section(settings.anthropic, "anthropic"),
                "azure": process_config_section(settings.azure, "azure")
            }
        }

        return {
            "config": config_data,
            "timestamp": time.time(),
            "note": "敏感信息已脱敏处理"
        }

    except Exception as e:
        logger.error("获取配置信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/config/environment")
async def get_environment_variables(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取环境变量信息（脱敏处理）

    Returns:
        脱敏后的环境变量
    """
    try:
        import os

        # 定义需要显示的环境变量前缀
        relevant_prefixes = [
            'AI_GEN_HUB_',
            'OPENAI_',
            'GOOGLE_',
            'ANTHROPIC_',
            'REDIS_',
            'DATABASE_',
            'POSTGRES_',
            'MYSQL_'
        ]

        # 敏感关键词
        sensitive_keywords = [
            'password', 'secret', 'key', 'token', 'api_key',
            'private_key', 'auth', 'credential', 'pass'
        ]

        env_vars = {}

        for key, value in os.environ.items():
            # 只显示相关的环境变量
            if any(key.startswith(prefix) for prefix in relevant_prefixes):
                # 脱敏处理
                if any(keyword in key.lower() for keyword in sensitive_keywords):
                    if value:
                        env_vars[key] = "***" + value[-4:] if len(value) > 4 else "***"
                    else:
                        env_vars[key] = "***"
                else:
                    env_vars[key] = value

        # 添加一些系统环境变量
        system_vars = {}
        for key in ['PATH', 'HOME', 'USER', 'SHELL', 'LANG', 'TZ']:
            if key in os.environ:
                system_vars[key] = os.environ[key]

        return {
            "application_vars": env_vars,
            "system_vars": system_vars,
            "total_env_vars": len(os.environ),
            "timestamp": time.time(),
            "note": "仅显示相关环境变量，敏感信息已脱敏"
        }

    except Exception as e:
        logger.error("获取环境变量失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/api/config/runtime")
async def get_runtime_info(
    request: Request,
    _: bool = Depends(check_debug_access)
):
    """获取运行时信息

    Returns:
        Python运行时信息
    """
    try:
        import sys
        import platform

        runtime_info = {
            "python": {
                "version": sys.version,
                "version_info": {
                    "major": sys.version_info.major,
                    "minor": sys.version_info.minor,
                    "micro": sys.version_info.micro
                },
                "executable": sys.executable,
                "platform": sys.platform,
                "implementation": platform.python_implementation()
            },
            "system": {
                "platform": platform.platform(),
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "architecture": platform.architecture()
            },
            "modules": {
                "installed_packages": len(sys.modules),
                "path": sys.path[:5]  # 只显示前5个路径
            }
        }

        # 获取已安装的重要包版本
        important_packages = [
            'fastapi', 'uvicorn', 'pydantic', 'sqlalchemy',
            'redis', 'httpx', 'structlog', 'prometheus_client'
        ]

        package_versions = {}
        for package in important_packages:
            try:
                import importlib.metadata
                version = importlib.metadata.version(package)
                package_versions[package] = version
            except:
                package_versions[package] = "未安装"

        runtime_info["packages"] = package_versions

        return {
            "runtime": runtime_info,
            "timestamp": time.time()
        }

    except Exception as e:
        logger.error("获取运行时信息失败", error=str(e))
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/api/generate-token")
async def generate_jwt_token(
    request: Request,
    token_request: TokenGenerateRequest,
    _: bool = Depends(check_debug_access)
):
    """生成JWT Token

    Args:
        token_request: Token生成请求参数

    Returns:
        生成的Token信息
    """
    if not JWT_AVAILABLE:
        raise HTTPException(status_code=500, detail="JWT库未安装")

    try:
        settings = request.app.state.settings
        if not settings:
            raise HTTPException(status_code=500, detail="配置未初始化")

        # 获取JWT配置
        secret_key = settings.security.jwt_secret_key
        algorithm = settings.security.jwt_algorithm

        # 创建payload
        now = datetime.utcnow()
        payload = {
            'sub': token_request.user_id,  # subject (用户ID)
            'iat': now,  # issued at (签发时间)
            'exp': now + timedelta(minutes=token_request.expire_minutes),  # expiration time (过期时间)
            'type': 'access_token',
            'scope': token_request.scope
        }

        # 生成token
        token = jwt.encode(payload, secret_key, algorithm=algorithm)

        logger.info(
            "生成JWT Token",
            user_id=token_request.user_id,
            expire_minutes=token_request.expire_minutes,
            scope=token_request.scope
        )

        return {
            "token": token,
            "user_id": token_request.user_id,
            "scope": token_request.scope,
            "created_at": now.isoformat(),
            "expires_at": (now + timedelta(minutes=token_request.expire_minutes)).isoformat(),
            "expire_minutes": token_request.expire_minutes
        }

    except Exception as e:
        logger.error("生成Token失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"生成Token失败: {str(e)}")


@router.post("/api/verify-token")
async def verify_jwt_token(
    request: Request,
    token_request: TokenVerifyRequest,
    _: bool = Depends(check_debug_access)
):
    """验证JWT Token

    Args:
        token_request: Token验证请求参数

    Returns:
        Token验证结果
    """
    if not JWT_AVAILABLE:
        raise HTTPException(status_code=500, detail="JWT库未安装")

    try:
        settings = request.app.state.settings
        if not settings:
            raise HTTPException(status_code=500, detail="配置未初始化")

        # 获取JWT配置
        secret_key = settings.security.jwt_secret_key
        algorithm = settings.security.jwt_algorithm

        # 验证token
        payload = jwt.decode(token_request.token, secret_key, algorithms=[algorithm])

        logger.info("验证Token成功", user_id=payload.get('sub'))

        return {
            "valid": True,
            "payload": payload,
            "user_id": payload.get('sub'),
            "scope": payload.get('scope'),
            "expires_at": datetime.fromtimestamp(payload.get('exp')).isoformat() if payload.get('exp') else None
        }

    except jwt.ExpiredSignatureError:
        logger.warning("Token已过期")
        return {
            "valid": False,
            "error": "Token已过期"
        }
    except jwt.InvalidTokenError as e:
        logger.warning("Token无效", error=str(e))
        return {
            "valid": False,
            "error": f"Token无效: {str(e)}"
        }
    except Exception as e:
        logger.error("验证Token失败", error=str(e))
        raise HTTPException(status_code=500, detail=f"验证Token失败: {str(e)}")
